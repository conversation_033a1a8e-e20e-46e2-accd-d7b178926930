name: flutterapp1
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  google_fonts: ^4.0.4
  flutter_screenutil: ^5.5.3+2
  shared_preferences: ^2.2.2
  percent_indicator: ^4.2.2
  loading_animation_widget: ^1.3.0
  wave: ^0.2.2
  version: ^3.0.0+1
  local_auth: ^2.1.7
  fl_chart: ^0.65.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: MuseoModerno-Bold
      fonts:
        - asset: fonts/MuseoModerno-Bold.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
    - family: MuseoModerno-Regular
      fonts:
        - asset: fonts/MuseoModerno-Regular.ttf

    - family: MuseoModerno-Medium
      fonts:
        - asset: fonts/MuseoModerno-Medium.ttf

    - family: Poppins-SemiBold
      fonts:
        - asset: fonts/Poppins-SemiBold.ttf        

    - family: MuseoModerno-SemiBold
      fonts:
        - asset: fonts/MuseoModerno-SemiBold.ttf

    - family: Urbanist-Bold
      fonts:
        - asset: fonts/Urbanist-Bold.ttf

    - family: Urbanist-Regular
      fonts:
        - asset: fonts/Urbanist-Regular.ttf

    - family: Urbanist-SemiBold
      fonts:
        - asset: fonts/Urbanist-SemiBold.ttf

    - family: Urbanist-Medium
      fonts:
        - asset: fonts/Urbanist-Medium.ttf
        
    - family: Urbanist-ExtraBold
      fonts:
        - asset: fonts/Urbanist-ExtraBold.ttf

    - family: Lexend-Regular
      fonts:
        - asset: fonts/Lexend-Regular.ttf
        
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
